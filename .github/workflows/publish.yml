name: Publish to PyPI

on:
  release:
    types:
      - published

permissions:
  contents: read

jobs:
  publish:
    environment:
      name: pypi
      url: https://pypi.org/p/openai-agents
    permissions:
      id-token: write # Important for trusted publishing to PyPI
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: fake-for-tests

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Build package
        run: uv build
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
