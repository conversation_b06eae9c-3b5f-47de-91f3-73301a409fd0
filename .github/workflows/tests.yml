name: Tests

on:
  push:
    branches:
      - main
  pull_request:
    # All PRs, including stacked PRs

env:
  UV_FROZEN: "1"

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Run lint
        run: make lint

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Run typecheck
        run: make mypy

  tests:
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: fake-for-tests
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Run tests with coverage
        run: make coverage

  build-docs:
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: fake-for-tests
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Build docs
        run: make build-docs

  old_versions:
    runs-on: ubuntu-latest
    env:
      OPENAI_API_KEY: fake-for-tests
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Run tests
        run: make old_version_tests
