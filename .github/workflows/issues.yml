name: Close inactive issues
on:
  schedule:
    - cron: "30 1 * * *"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 7
          days-before-issue-close: 3
          stale-issue-label: "stale"
          stale-issue-message: "This issue is stale because it has been open for 7 days with no activity."
          close-issue-message: "This issue was closed because it has been inactive for 3 days since being marked as stale."
          any-of-issue-labels: 'question,needs-more-info'
          days-before-pr-stale: 10
          days-before-pr-close: 7
          stale-pr-label: "stale"
          stale-pr-message: "This PR is stale because it has been open for 10 days with no activity."
          close-pr-message: "This PR was closed because it has been inactive for 7 days since being marked as stale."
          repo-token: ${{ secrets.GITHUB_TOKEN }}
