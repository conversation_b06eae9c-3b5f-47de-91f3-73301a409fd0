# Examples

Check out a variety of sample implementations of the SDK in the examples section of the [repo](https://github.com/openai/openai-agents-python/tree/main/examples). The examples are organized into several categories that demonstrate different patterns and capabilities.


## Categories

- **[agent_patterns](https://github.com/openai/openai-agents-python/tree/main/examples/agent_patterns):**
  Examples in this category illustrate common agent design patterns, such as

    - Deterministic workflows
    - Agents as tools
    - Parallel agent execution

- **[basic](https://github.com/openai/openai-agents-python/tree/main/examples/basic):**
  These examples showcase foundational capabilities of the SDK, such as

    - Dynamic system prompts
    - Streaming outputs
    - Lifecycle events

- **[tool examples](https://github.com/openai/openai-agents-python/tree/main/examples/tools):**
  Learn how to implement OAI hosted tools such as web search and file search,
   and integrate them into your agents.

- **[model providers](https://github.com/openai/openai-agents-python/tree/main/examples/model_providers):**
  Explore how to use non-OpenAI models with the SDK.

- **[handoffs](https://github.com/openai/openai-agents-python/tree/main/examples/handoffs):**
  See practical examples of agent handoffs.

- **[mcp](https://github.com/openai/openai-agents-python/tree/main/examples/mcp):**
  Learn how to build agents with MCP.

- **[customer_service](https://github.com/openai/openai-agents-python/tree/main/examples/customer_service)** and **[research_bot](https://github.com/openai/openai-agents-python/tree/main/examples/research_bot):**
  Two more built-out examples that illustrate real-world applications

    - **customer_service**: Example customer service system for an airline.
    - **research_bot**: Simple deep research clone.

- **[voice](https://github.com/openai/openai-agents-python/tree/main/examples/voice):**
  See examples of voice agents, using our TTS and STT models.
