@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: normal;
    font-weight: 400;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Regular.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: italic;
    font-weight: 400;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-RegularItalic.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: normal;
    font-weight: 500;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Medium.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: italic;
    font-weight: 500;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-MediumItalic.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: normal;
    font-weight: 600;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Semibold.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: italic;
    font-weight: 600;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-SemiboldItalic.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: normal;
    font-weight: 700;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Bold.woff2")
        format("woff2");
}

@font-face {
    font-display: swap;
    font-family: "OpenAI Sans";
    font-style: italic;
    font-weight: 700;
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-BoldItalic.woff2")
        format("woff2");
}

/* 
    Root variables that apply to all color schemes.
    Material for MkDocs automatically switches data-md-color-scheme
    between "default" (light) and "slate" (dark) when you use the toggles.
*/
:root {
    /* Font families */
    --md-text-font: "OpenAI Sans", -apple-system, system-ui, Helvetica, Arial,
        sans-serif;
    --md-typeface-heading: "OpenAI Sans", -apple-system, system-ui, Helvetica,
        Arial, sans-serif;

    /* Global color variables */
    --md-default-fg-color: #212121;
    --md-default-bg-color: #ffffff;
    --md-primary-fg-color: #000;
    --md-accent-fg-color: #000;

    /* Code block theming */
    --md-code-fg-color: red;
    --md-code-bg-color: #f5f5f5;

    /* Tables, blockquotes, etc. */
    --md-table-row-border-color: #e0e0e0;
    --md-admonition-bg-color: #f8f8f8;
    --md-admonition-title-fg-color: #373737;
    --md-default-fg-color--light: #000;

    --md-typeset-a-color: #000;
    --md-accent-fg-color: #000;

    --md-code-fg-color: #000;
}

/* Header styling */
.md-header {
    background-color: #000;
}

.md-header--shadow {
    box-shadow: none;
}

.md-content .md-typeset h1 {
    color: #000;
}

.md-typeset p,
.md-typeset li {
    font-size: 16px;
}

.md-typeset__table p {
    line-height: 1em;
}

.md-nav {
    font-size: 14px;
}
.md-nav__title {
    color: #000;
    font-weight: 600;
}

.md-typeset h1,
.md-typeset h2,
.md-typeset h3,
.md-typeset h4 {
    font-weight: 600;
}

.md-typeset h1 code {
    color: #000;
    padding: 0;
    background-color: transparent;
}
.md-footer {
    display: none;
}

.md-header__title {
    margin-left: 0 !important;
}

.md-typeset .admonition,
.md-typeset details {
    border: none;
    outline: none;
    border-radius: 8px;
    overflow: hidden;
}

.md-typeset pre > code {
    font-size: 14px;
}

.md-typeset__table code {
    font-size: 14px;
}

/* Custom link styling */
.md-content a {
    text-decoration: none;
}

.md-content a:hover {
    text-decoration: underline;
}

/* Code block styling */
.md-content .md-code__content {
    border-radius: 8px;
}

.md-clipboard.md-icon {
    color: #9e9e9e;
}

/* Reset scrollbar styling to browser default with high priority */
.md-sidebar__scrollwrap {
    scrollbar-color: auto !important;
}
